---
import type { LayoutProps } from "@/env.d.ts";
import "@/styles/global.css";
import "@/styles/fix-bottom-space.css";
import CriticalCSS from "@/components/CriticalCSS.astro";
import SEOHead from "@/components/SEOHead.astro";
import Analytics from "@/components/Analytics.astro";
import LoadingScreen from "@/components/LoadingScreen.astro";
import Footer from "@/components/Footer.astro";
import { ClientRouter } from "astro:transitions";

export interface Props extends LayoutProps {
  schema?: "Person" | "WebSite" | "Article" | "CreativeWork";
  publishedTime?: string;
  modifiedTime?: string;
  tags?: string[];
}

const {
  title,
  description = "Portfolio interaktif Muhammad Trinanda - Cyberpunk Edition. Mahasiswa Akuntansi Syariah dengan passion di bidang teknologi, data analysis, dan desain.",
  image = "/og-image.jpg",
  canonical,
  schema = "WebSite",
  publishedTime,
  modifiedTime,
  tags = [],
} = Astro.props;

// Generate canonical URL
const canonicalURL = canonical || new URL(Astro.url.pathname, Astro.site);
const currentYear = new Date().getFullYear();
---

<!doctype html>
<html lang="id">
  <head>
    <CriticalCSS />
    <ClientRouter />
    <SEOHead
      title={title}
      description={description}
      image={image}
      url={canonicalURL.href}
      schema={schema}
      publishedTime={publishedTime}
      modifiedTime={modifiedTime}
      tags={tags}
    />

    <!-- Canonical URL -->
    <link rel="canonical" href={canonicalURL} />

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- SEO Meta Tags -->
    <meta name="generator" content={Astro.generator} />
    <meta name="author" content="Muhammad Trinanda" />
    <meta
      name="keywords"
      content="Muhammad Trinanda, Portfolio, Akuntansi Syariah, Data Analysis, Web Design, Cyberpunk, Dashboard, Visualization"
    />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="Indonesian" />
    <meta name="copyright" content={`© ${currentYear} Muhammad Trinanda`} />

    <title>{title}</title>

    <!-- Theme System CSS -->
    <link rel="stylesheet" href="/src/styles/theme-system.css" />
    <link rel="stylesheet" href="/src/styles/fix-bottom-space.css" />
    <link rel="stylesheet" href="/src/styles/playground-space-fix.css" />

    <!-- Inline CSS to fix bottom space immediately -->
    <style>
      html,
      body {
        margin: 0 !important;
        padding: 0 !important;
        height: 100% !important;
        max-height: 100vh !important;
      }

      body {
        display: block !important;
        min-height: 100vh !important;
        overflow-x: hidden !important;
      }

      main {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
      }

      footer {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
        margin-top: 0 !important;
      }

      section:last-child {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
      }

      .relative.z-10 {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
      }

      /* Specific fixes for CodePlayground space */
      #playground {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
      }

      #code-playground {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
      }

      /* Force remove space after any section */
      section:last-of-type {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
      }

      /* Ensure footer sticks to content */
      footer {
        margin-top: 0 !important;
      }

      /* Fix the wrapper div that causes space */
      .relative.z-10 {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
        min-height: auto !important;
      }

      /* Force remove all space from body and html */
      html {
        height: auto !important;
        min-height: 100vh !important;
      }

      body {
        height: auto !important;
        min-height: 100vh !important;
      }

      /* Aggressive fix for space after playground */
      #playground + * {
        margin-top: 0 !important;
      }

      /* Remove space from all astro components */
      [data-astro-cid] {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
      }
    </style>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com" />
    <link
      rel="preload"
      href="/fonts/orbitron-v29-latin-regular.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="/fonts/orbitron-v29-latin-700.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link rel="preload" href="/assets/css/critical.css" as="style" />
    <link rel="preload" href="/assets/js/main.js" as="script" />

    <!-- Critical CSS -->
    <link rel="stylesheet" href="/assets/css/critical.css" />

    <!-- Font CSS -->
    <link rel="stylesheet" href="/fonts/orbitron.css" />

    <!-- PWA Theme -->
    <meta name="theme-color" content="#0a0a0a" />
    <meta name="msapplication-TileColor" content="#0a0a0a" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:type" content="website" />
    <meta property="og:url" content={canonicalURL} />
    <meta property="og:image" content={new URL(image, Astro.site).toString()} />
    <meta property="og:image:alt" content={`${title} - Portfolio Screenshot`} />
    <meta property="og:site_name" content="Muhammad Trinanda Portfolio" />
    <meta property="og:locale" content="id_ID" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={title} />
    <meta name="twitter:description" content={description} />
    <meta
      name="twitter:image"
      content={new URL(image, Astro.site).toString()}
    />
    <meta
      name="twitter:image:alt"
      content={`${title} - Portfolio Screenshot`}
    />

    <!-- Structured Data -->
    <script type="application/ld+json" is:inline>
      {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "Muhammad Trinanda",
        "jobTitle": "Mahasiswa Akuntansi Syariah",
        "description": "Mahasiswa Akuntansi Syariah dengan passion di bidang teknologi, data analysis, dan desain",
        "url": "https://trinanda-portfolio.vercel.app",
        "sameAs": [
          "https://linkedin.com/in/muhammad-trinanda",
          "https://instagram.com/trinanda.id"
        ],
        "knowsAbout": [
          "Akuntansi Syariah",
          "Data Analysis",
          "Web Design",
          "Dashboard Design",
          "Data Visualization"
        ],
        "alumniOf": {
          "@type": "EducationalOrganization",
          "name": "Universitas"
        }
      }
    </script>
  </head>
  <body class="cyber-grid">
    <!-- Loading Screen -->
    <LoadingScreen showProgress={true} theme="cyberpunk" />

    <!-- Particle Background -->
    <div id="particles-bg" class="particles-bg"></div>

    <!-- Matrix Rain Background -->
    <canvas
      id="matrix-canvas"
      class="fixed inset-0 -z-10 pointer-events-none opacity-10"></canvas>

    <!-- Main Content -->
    <div class="relative z-10">
      <slot />
    </div>

    <!-- Footer -->
    <Footer />

    <!-- Matrix Rain Script -->
    <script>
      // Matrix Rain Effect
      const canvas = document.getElementById(
        "matrix-canvas"
      ) as HTMLCanvasElement | null;
      if (canvas) {
        const ctx = canvas.getContext("2d");
        if (ctx) {
          function resizeCanvas() {
            if (canvas) {
              canvas.width = window.innerWidth;
              canvas.height = window.innerHeight;
            }
          }

          resizeCanvas();

          const matrix =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}";
          const matrixArray = matrix.split("");

          const fontSize = 10;
          let columns = canvas.width / fontSize;

          let drops: number[] = [];
          for (let x = 0; x < columns; x++) {
            drops[x] = 1;
          }

          function drawMatrix() {
            if (canvas && ctx) {
              ctx.fillStyle = "rgba(0, 0, 0, 0.04)";
              ctx.fillRect(0, 0, canvas.width, canvas.height);

              ctx.fillStyle = "#00ff41";
              ctx.font = fontSize + "px monospace";

              for (let i = 0; i < drops.length; i++) {
                const text =
                  matrixArray[Math.floor(Math.random() * matrixArray.length)];
                ctx.fillText(text, i * fontSize, drops[i] * fontSize);

                if (
                  drops[i] * fontSize > canvas.height &&
                  Math.random() > 0.975
                ) {
                  drops[i] = 0;
                }
                drops[i]++;
              }
            }
          }

          setInterval(drawMatrix, 35);

          // Resize handler
          window.addEventListener("resize", () => {
            if (canvas) {
              resizeCanvas();
              columns = canvas.width / fontSize;
              drops = [];
              for (let x = 0; x < columns; x++) {
                drops[x] = 1;
              }
            }
          });
        }
      }
    </script>

    <!-- Service Worker Registration -->
    <script>
      if ("serviceWorker" in navigator) {
        window.addEventListener("load", () => {
          navigator.serviceWorker
            .register("/sw.js")
            .then((registration) => {
              console.log("SW registered: ", registration);
            })
            .catch((registrationError) => {
              console.log("SW registration failed: ", registrationError);
            });
        });
      }
    </script>

    <!-- Initialize Performance Monitoring -->
    <script>
      import("../utils/performance.js").then(
        ({ PerformanceMonitor, LazyLoader, CacheManager }) => {
          // Initialize performance monitoring
          window.performanceMonitor = new PerformanceMonitor();
          window.lazyLoader = new LazyLoader();
          window.cacheManager = new CacheManager();

          // Cache critical resources
          const criticalResources = [
            "/assets/css/critical.css",
            "/assets/js/main.js",
            "/My Profile.png",
          ];

          window.cacheManager.cacheResources(criticalResources);
        }
      );
    </script>

    <!-- Main JavaScript -->
    <script src="/assets/js/main.js" is:inline></script>

    <!-- Service Worker Registration -->
    <script>
      if ("serviceWorker" in navigator) {
        window.addEventListener("load", () => {
          navigator.serviceWorker
            .register("/sw.js")
            .then((registration) => {
              console.log("SW registered: ", registration);
            })
            .catch((registrationError) => {
              console.log("SW registration failed: ", registrationError);
            });
        });
      }
    </script>

    <!-- Initialize Theme System -->
    <script>
      import("../utils/theme-system.js").then(({ default: ThemeSystem }) => {
        // Initialize theme system
        window.themeSystem = new ThemeSystem();

        // Add theme observer for Three.js effects
        window.themeSystem.addObserver((themeName, theme) => {
          // Update particle colors based on theme
          if (window.particleSystem) {
            window.particleSystem.updateTheme(theme);
          }

          // Update GSAP animations based on theme
          if (window.animationController) {
            window.animationController.updateTheme(theme);
          }
        });
      });
    </script>

    <!-- Initialize Error Tracking -->
    <script>
      import("../utils/error-tracker.js").then(({ default: ErrorTracker }) => {
        // Initialize error tracking
        window.errorTracker = new ErrorTracker({
          enableConsoleLogging: true,
          enableRemoteLogging: false, // Set to true in production
          enableUserFeedback: true,
          enablePerformanceTracking: true,
          enableNetworkTracking: true,
        });

        // Log successful initialization
        console.log("✅ Error tracking initialized");
      });
    </script>

    <!-- Initialize Content Manager -->
    <script>
      import("../utils/content-manager.js").then(
        ({ default: ContentManager }) => {
          // Initialize content management
          window.contentManager = new ContentManager();

          // Preload critical content
          const criticalContent = [
            { type: "projects", id: "featured" },
            { type: "skills", id: "primary" },
          ];

          window.contentManager.preloadContent(criticalContent);
        }
      );
    </script>

    <!-- Analytics -->
    <Analytics />
  </body>
</html>
