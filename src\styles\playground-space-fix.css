/* 
 * AGGRESSIVE FIX FOR SPACE AFTER INTERACTIVE CODE PLAYGROUND
 * This file contains nuclear-level CSS to eliminate any space after the playground section
 */

/* FORCE REMOVE ALL BOTTOM SPACING FROM PLAYGROUND SECTION */
#playground {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  border-bottom: none !important;
  overflow: hidden !important;
  position: relative !important;
}

/* Remove any pseudo-elements that might create space */
#playground::after,
#playground::before {
  display: none !important;
  content: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* Force container inside playground to have no bottom space */
#playground .container {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  border-bottom: none !important;
}

#playground .container > * {
  margin-bottom: 0 !important;
}

#playground .container > *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* FORCE CODE PLAYGROUND COMPONENT TO HAVE NO BOTTOM SPACE */
#code-playground {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  border-bottom: none !important;
  overflow: hidden !important;
}

#code-playground::after,
#code-playground::before {
  display: none !important;
  content: none !important;
  height: 0 !important;
}

/* Force all children of code playground to have no bottom space */
#code-playground > * {
  margin-bottom: 0 !important;
}

#code-playground > *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

#code-playground * {
  margin-bottom: 0 !important;
}

#code-playground *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* FORCE GRID LAYOUT TO HAVE NO BOTTOM SPACE */
#code-playground .grid {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

#code-playground .grid > * {
  margin-bottom: 0 !important;
}

#code-playground .grid > *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* ENSURE FOOTER IMMEDIATELY FOLLOWS PLAYGROUND */
#playground + footer,
#playground ~ footer,
section:last-of-type + footer {
  margin-top: 0 !important;
  padding-top: 2rem !important;
  border-top: 1px solid rgba(6, 182, 212, 0.3) !important;
}

/* FORCE BODY AND HTML TO END IMMEDIATELY AFTER CONTENT */
body {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  overflow-x: hidden !important;
}

html {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  overflow-x: hidden !important;
}

/* FORCE MAIN CONTAINER TO HAVE NO BOTTOM SPACE */
main {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

main > * {
  margin-bottom: 0 !important;
}

main > *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* ENSURE THREE.JS BACKGROUND DOESN'T INTERFERE WITH LAYOUT */
#three-background {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: -1 !important;
  pointer-events: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

#three-canvas {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* NUCLEAR OPTION: FORCE REMOVE SPACE FROM ALL SECTIONS */
section {
  margin-bottom: 0 !important;
}

section:last-of-type,
section:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  border-bottom: none !important;
}

/* FORCE REMOVE SPACE FROM ALL DIRECT CHILDREN OF MAIN */
main > section {
  margin-bottom: 0 !important;
}

main > section:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* AGGRESSIVE FIX FOR ANY REMAINING SPACE */
* {
  box-sizing: border-box !important;
}

*:last-child {
  margin-bottom: 0 !important;
}

/* FORCE VIEWPORT HEIGHT CONSTRAINT */
html {
  height: auto !important;
  min-height: 100vh !important;
  max-height: none !important;
}

body {
  height: auto !important;
  min-height: 100vh !important;
  max-height: none !important;
}

/* ENSURE NO SPACE BETWEEN SECTIONS */
section + section {
  margin-top: 0 !important;
}

/* FORCE REMOVE ANY MARGIN/PADDING FROM LAYOUT WRAPPER */
.relative.z-10 {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  min-height: auto !important;
}

/* FINAL NUCLEAR OPTION: FORCE HEIGHT CALCULATION */
#playground {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
}

#code-playground {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
}

/* ENSURE ASTRO COMPONENTS DON'T ADD EXTRA SPACE */
[data-astro-cid] {
  margin-bottom: 0 !important;
}

[data-astro-cid]:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* FORCE REMOVE SPACE FROM TAILWIND CLASSES */
.py-6 {
  padding-bottom: 0 !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

/* ENSURE CONTAINER CLASSES DON'T ADD SPACE */
.container {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.mx-auto {
  margin-bottom: 0 !important;
}

/* FINAL DESPERATE MEASURE: NEGATIVE MARGIN IF NEEDED */
#playground + * {
  margin-top: -1px !important;
}

footer {
  margin-top: -1px !important;
}
