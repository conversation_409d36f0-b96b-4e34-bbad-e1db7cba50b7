/* Fix Bottom Space - Critical CSS to remove all bottom spacing */

/* Force remove all bottom margins and padding */
html, body {
  margin: 0 !important;
  padding: 0 !important;
  height: 100% !important;
  max-height: 100vh !important;
  overflow-x: hidden !important;
}

/* Remove flexbox layout that causes conflicts */
body {
  display: block !important;
  min-height: 100vh !important;
}

/* Make main content take full space */
main {
  margin: 0 !important;
  padding: 0 !important;
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Remove all bottom spacing from footer */
footer {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  margin-top: auto !important;
}

/* Remove spacing from all sections */
section {
  margin-bottom: 0 !important;
}

section:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Remove spacing from containers */
.container {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Remove spacing from layout wrappers */
.relative.z-10 {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Target specific problematic elements */
#playground {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Remove any pseudo-elements */
*::after,
*::before {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Force remove margin collapse */
* {
  margin-block-end: 0 !important;
  margin-block-start: 0 !important;
}

/* Remove space from Astro components */
[data-astro-cid] {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Ensure viewport height is properly used */
html {
  box-sizing: border-box !important;
}

*, *::before, *::after {
  box-sizing: inherit !important;
}

/* Remove any default browser spacing */
body {
  margin-block-start: 0 !important;
  margin-block-end: 0 !important;
  margin-inline-start: 0 !important;
  margin-inline-end: 0 !important;
}

/* Force footer to stick to content */
footer {
  position: relative !important;
  bottom: 0 !important;
}

/* Additional aggressive fixes */
body > * {
  margin-bottom: 0 !important;
}

body > *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Remove space from all direct children of main */
main > * {
  margin-bottom: 0 !important;
}

main > *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Ensure no space after Layout component */
[data-astro-cid] > *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Force viewport height constraint */
html {
  height: 100vh !important;
  max-height: 100vh !important;
}

body {
  height: auto !important;
  min-height: 100vh !important;
  overflow-y: auto !important;
}

/* Specific fixes for CodePlayground space issue */
#code-playground {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

#playground {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

#playground .container {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

#playground > div {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Remove space from all children of playground */
#playground * {
  margin-bottom: 0 !important;
}

#playground *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Force remove any pseudo-elements from playground */
#playground::after,
#playground::before,
#code-playground::after,
#code-playground::before {
  display: none !important;
}

/* Ensure no space between sections */
section + section {
  margin-top: 0 !important;
}

/* Force footer to immediately follow content */
footer {
  margin-top: 0 !important;
}

/* AGGRESSIVE FIX FOR PLAYGROUND SPACE ISSUE */
/* Force remove any space after playground section */
#playground {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  border-bottom: none !important;
  overflow: hidden !important;
}

#playground::after,
#playground::before {
  display: none !important;
  content: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Force container inside playground to have no bottom space */
#playground .container {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

#playground .container > * {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

#playground .container > *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Force code playground component to have no bottom space */
#code-playground {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  border-bottom: none !important;
}

#code-playground::after,
#code-playground::before {
  display: none !important;
  content: none !important;
}

/* Force all children of code playground to have no bottom space */
#code-playground * {
  margin-bottom: 0 !important;
}

#code-playground *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Ensure footer immediately follows playground */
#playground + footer,
#playground ~ footer {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* Force body to end immediately after last content */
body {
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
}

/* Aggressive fix for any remaining space */
main {
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
}

main > *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Force viewport constraint to prevent extra space */
html {
  overflow-x: hidden !important;
}

body {
  overflow-x: hidden !important;
  min-height: 100vh !important;
  max-height: none !important;
}

/* Ensure Three.js background doesn't interfere */
#three-background {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: -1 !important;
  pointer-events: none !important;
}

#three-canvas {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: block !important;
}

/* NUCLEAR OPTION - Remove ALL possible space sources */
* {
  margin-bottom: 0 !important;
}

*:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Target the wrapper div specifically */
.relative.z-10 {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  height: auto !important;
  min-height: auto !important;
}

/* Ensure body height is exactly what's needed */
body {
  height: auto !important;
  min-height: 100vh !important;
  max-height: none !important;
}

html {
  height: auto !important;
  min-height: 100vh !important;
  max-height: none !important;
}

/* Remove space from all possible elements */
div, section, main, article, aside, nav, header, footer {
  margin-bottom: 0 !important;
}

/* Force remove space after last elements */
body > *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.relative.z-10 > *:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}
