---
import Layout from "@/layouts/Layout.astro";
import Header from "@/components/Header.astro";

import SkipLink from "@/components/SkipLink.astro";
import { Image } from "astro:assets";
import profileImage from "@/assets/My Profile.png";
import InteractiveCard from "@/components/InteractiveCard.astro";
import LazySection from "@/components/LazySection.astro";
import ThreeBackground from "@/components/ThreeBackground.astro";
import ThemeCustomizer from "@/components/ThemeCustomizer.astro";
import ThemeSwitcher from "@/components/ThemeSwitcher.astro";
import ContactForm from "@/components/ContactForm.astro";
import AnalyticsDashboard from "@/components/AnalyticsDashboard.astro";
import AdvancedSearch from "@/components/AdvancedSearch.astro";
import CodePlayground from "@/components/CodePlayground.astro";
---

<Layout
  title="Muhammad Trinanda - Cyberpunk Portfolio"
  description="Portfolio interaktif Muhammad <PERSON>nanda - Mahasiswa Akuntansi Syariah dengan passion di bidang teknologi, data analysis, dan desain. Cyberpunk Edition."
>
  <SkipLink />
  <Header />
  <ThreeBackground
    particleCount={1000}
    enableInteraction={true}
    colorScheme="cyberpunk"
  />
  <ThemeCustomizer />
  <ThemeSwitcher position="top-right" showLabel={false} compact={true} />

  <!-- Main Content -->
  <main id="main-content">
    <!-- Hero Section -->
    <section
      id="home"
      class="min-h-screen flex items-center justify-center relative overflow-hidden pt-20"
      aria-labelledby="hero-heading"
      transition:name="hero-section"
    >
      <div class="container mx-auto px-6 py-20 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- Hero Content -->
          <div class="space-y-8">
            <div class="space-y-6">
              <div
                class="text-sm font-mono text-cyan-400 tracking-widest uppercase"
                aria-label="Welcome message"
              >
                &lt;/WELCOME TO THE MATRIX&gt;
              </div>
              <h1
                id="hero-heading"
                class="text-4xl md:text-6xl lg:text-7xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-pink-400 to-purple-400 glitch leading-tight"
              >
                MUHAMMAD<br />TRINANDA
              </h1>
              <div
                class="text-xl lg:text-2xl text-gray-300 font-mono"
                role="banner"
              >
                <span class="text-cyan-400 neon-glow" aria-hidden="true"
                  >&gt;</span
                >
                <span
                  id="typed-text"
                  class="typing-animation ml-2"
                  aria-live="polite"
                  aria-label="Dynamic role description"></span>
                <span class="cursor" aria-hidden="true">|</span>
              </div>
            </div>

            <p class="text-lg text-gray-400 leading-relaxed max-w-lg">
              Final-Semester Student of <span
                class="text-cyan-400 font-semibold"
                >Sharia Accounting, UINSU</span
              ><br />
              <span class="text-pink-400 font-semibold">Graphic Designer</span> •
              <span class="text-green-400 font-semibold">Data Analyst</span> •
              <span class="text-purple-400 font-semibold">Web Developer</span>
            </p>

            <div class="flex flex-col sm:flex-row gap-4">
              <a href="#about" class="cyber-button primary group">
                <span class="relative z-10">EXPLORE MY WORK</span>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"
                >
                </div>
              </a>
              <a href="#contact" class="cyber-button secondary group">
                <span class="relative z-10">GET IN TOUCH</span>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"
                >
                </div>
              </a>
            </div>

            <!-- Stats -->
            <div
              class="grid grid-cols-3 gap-4 pt-8 border-t border-gray-700/50"
            >
              <div class="text-center">
                <div class="text-2xl font-bold text-cyan-400 neon-glow">5+</div>
                <div class="text-sm text-gray-500 font-mono">YEARS EXP</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-pink-400 neon-glow">
                  50+
                </div>
                <div class="text-sm text-gray-500 font-mono">PROJECTS</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-400 neon-glow">
                  100%
                </div>
                <div class="text-sm text-gray-500 font-mono">PASSION</div>
              </div>
            </div>
          </div>

          <!-- Hero Image -->
          <div class="relative">
            <div class="relative w-full max-w-lg mx-auto">
              <!-- Holographic frame -->
              <div
                class="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-pink-500/20 rounded-2xl blur-2xl animate-pulse"
              >
              </div>

              <!-- Main image container -->
              <div
                class="relative neon-border rounded-2xl overflow-hidden bg-gradient-to-br from-gray-900 to-black p-1"
              >
                <div class="relative rounded-xl overflow-hidden">
                  <Image
                    src={profileImage}
                    alt="Muhammad Trinanda Profile"
                    class="w-full h-auto object-cover rounded-xl"
                    loading="eager"
                    format="webp"
                    quality={90}
                    width={500}
                    height={600}
                  />
                  <!-- Scan lines effect -->
                  <div
                    class="absolute inset-0 bg-gradient-to-b from-transparent via-cyan-500/10 to-transparent animate-pulse"
                  >
                  </div>
                  <!-- Matrix rain effect overlay -->
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"
                  >
                  </div>
                </div>
              </div>

              <!-- Floating elements -->
              <div
                class="absolute -top-6 -right-6 w-12 h-12 bg-cyan-400/20 rounded-full pulse-neon border-2 border-cyan-400 flex items-center justify-center"
              >
                <div class="w-4 h-4 bg-cyan-400 rounded-full"></div>
              </div>
              <div
                class="absolute -bottom-6 -left-6 w-10 h-10 bg-pink-400/20 rounded-full pulse-neon border-2 border-pink-400 flex items-center justify-center"
              >
                <div class="w-3 h-3 bg-pink-400 rounded-full"></div>
              </div>
              <div
                class="absolute top-1/2 -left-10 w-8 h-8 bg-green-400/20 rounded-full pulse-neon border-2 border-green-400 flex items-center justify-center"
              >
                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
              </div>
              <div
                class="absolute top-1/4 -right-8 w-6 h-6 bg-purple-400/20 rounded-full pulse-neon border-2 border-purple-400 flex items-center justify-center"
              >
                <div class="w-1.5 h-1.5 bg-purple-400 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Animated background elements -->
      <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div
          class="absolute top-1/4 left-1/4 w-64 h-64 bg-cyan-500/5 rounded-full blur-3xl animate-pulse"
        >
        </div>
        <div
          class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-500/5 rounded-full blur-3xl animate-pulse"
        >
        </div>
        <div
          class="absolute top-3/4 left-1/2 w-48 h-48 bg-green-500/5 rounded-full blur-3xl animate-pulse"
        >
        </div>
      </div>
    </section>

    <!-- About Section -->
    <LazySection id="about" class="py-20 relative">
      <section aria-labelledby="about-heading">
        <div class="container mx-auto px-6">
          <div class="text-center mb-16">
            <h2
              class="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 mb-4"
            >
              &lt;ABOUT_ME/&gt;
            </h2>
            <p class="text-gray-400 font-mono">
              Discovering the person behind the code
            </p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div class="space-y-8">
              <div class="cyber-card">
                <h3 class="text-2xl font-bold text-cyan-400 mb-6 neon-glow">
                  Who Am I?
                </h3>
                <p class="text-gray-300 leading-relaxed mb-6">
                  I'm Muhammad Trinanda, a final-year <span
                    class="text-cyan-400 font-semibold"
                    >Sharia Accounting student at UINSU</span
                  >. I'm passionate about the intersection of finance,
                  technology, and design.
                </p>
                <p class="text-gray-300 leading-relaxed">
                  My journey combines traditional accounting principles with
                  modern digital solutions, creating innovative approaches to
                  financial analysis and business intelligence.
                </p>
              </div>

              <div class="cyber-card">
                <h3 class="text-2xl font-bold text-pink-400 mb-6 neon-glow">
                  My Mission
                </h3>
                <p class="text-gray-300 leading-relaxed">
                  To bridge the gap between <span
                    class="text-pink-400 font-semibold"
                    >financial expertise</span
                  > and
                  <span class="text-green-400 font-semibold"
                    >creative design</span
                  >, delivering data-driven solutions that are both analytically
                  sound and visually compelling.
                </p>
              </div>

              <div class="flex flex-wrap gap-4 mt-8">
                <span class="cyber-tag">Accounting Expert</span>
                <span class="cyber-tag">Creative Designer</span>
                <span class="cyber-tag">Data Analyst</span>
                <span class="cyber-tag">Problem Solver</span>
              </div>
            </div>

            <div class="relative">
              <div class="cyber-card p-8">
                <div class="space-y-6">
                  <div class="flex items-center space-x-4">
                    <div
                      class="w-12 h-12 bg-gradient-to-r from-cyan-400 to-pink-400 rounded-lg flex items-center justify-center"
                    >
                      <svg
                        class="w-6 h-6 text-black"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"
                        ></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="text-xl font-bold text-white">Education</h4>
                      <p class="text-gray-400">Sharia Accounting, UINSU</p>
                    </div>
                  </div>

                  <div class="flex items-center space-x-4">
                    <div
                      class="w-12 h-12 bg-gradient-to-r from-pink-400 to-purple-400 rounded-lg flex items-center justify-center"
                    >
                      <svg
                        class="w-6 h-6 text-black"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z"
                          clip-rule="evenodd"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="text-xl font-bold text-white">
                        Specialization
                      </h4>
                      <p class="text-gray-400">Financial Analysis & Design</p>
                    </div>
                  </div>

                  <div class="flex items-center space-x-4">
                    <div
                      class="w-12 h-12 bg-gradient-to-r from-green-400 to-cyan-400 rounded-lg flex items-center justify-center"
                    >
                      <svg
                        class="w-6 h-6 text-black"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        ></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="text-xl font-bold text-white">Experience</h4>
                      <p class="text-gray-400">5+ Years in Design & Analysis</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Skills Section -->
      <section
        id="skills"
        class="py-20 relative bg-gradient-to-b from-transparent to-gray-900/50"
      >
        <div class="container mx-auto px-6">
          <div class="text-center mb-16">
            <h2
              class="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-cyan-400 mb-4"
            >
              &lt;SKILLS_MATRIX/&gt;
            </h2>
            <p class="text-gray-400 font-mono">
              My technical arsenal and expertise
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Accounting & Finance -->
            <div class="skill-card group">
              <div
                class="skill-icon bg-gradient-to-r from-cyan-400 to-blue-500"
              >
                <svg
                  class="w-8 h-8 text-black"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
                  ></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-cyan-400 mb-3">
                Accounting & Finance
              </h3>
              <div class="space-y-3">
                <div class="skill-bar">
                  <div class="skill-label">Financial Analysis</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-cyan-400" style="width: 95%">
                    </div>
                  </div>
                </div>
                <div class="skill-bar">
                  <div class="skill-label">Sharia Accounting</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-cyan-400" style="width: 90%">
                    </div>
                  </div>
                </div>
                <div class="skill-bar">
                  <div class="skill-label">Budget Planning</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-cyan-400" style="width: 88%">
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Design -->
            <div class="skill-card group">
              <div
                class="skill-icon bg-gradient-to-r from-pink-400 to-purple-500"
              >
                <svg
                  class="w-8 h-8 text-black"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                    clip-rule="evenodd"></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-pink-400 mb-3">
                Graphic Design
              </h3>
              <div class="space-y-3">
                <div class="skill-bar">
                  <div class="skill-label">Adobe Creative Suite</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-pink-400" style="width: 92%">
                    </div>
                  </div>
                </div>
                <div class="skill-bar">
                  <div class="skill-label">UI/UX Design</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-pink-400" style="width: 85%">
                    </div>
                  </div>
                </div>
                <div class="skill-bar">
                  <div class="skill-label">Brand Identity</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-pink-400" style="width: 90%">
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Data Analysis -->
            <div class="skill-card group">
              <div
                class="skill-icon bg-gradient-to-r from-green-400 to-emerald-500"
              >
                <svg
                  class="w-8 h-8 text-black"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"
                  ></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-green-400 mb-3">
                Data Analysis
              </h3>
              <div class="space-y-3">
                <div class="skill-bar">
                  <div class="skill-label">Excel & Power BI</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-green-400" style="width: 93%">
                    </div>
                  </div>
                </div>
                <div class="skill-bar">
                  <div class="skill-label">Statistical Analysis</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-green-400" style="width: 87%">
                    </div>
                  </div>
                </div>
                <div class="skill-bar">
                  <div class="skill-label">Business Intelligence</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-green-400" style="width: 89%">
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Web Development -->
            <div class="skill-card group">
              <div
                class="skill-icon bg-gradient-to-r from-purple-400 to-indigo-500"
              >
                <svg
                  class="w-8 h-8 text-black"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                    clip-rule="evenodd"></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-purple-400 mb-3">
                Web Development
              </h3>
              <div class="space-y-3">
                <div class="skill-bar">
                  <div class="skill-label">HTML/CSS/JS</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-purple-400" style="width: 85%">
                    </div>
                  </div>
                </div>
                <div class="skill-bar">
                  <div class="skill-label">React/Astro</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-purple-400" style="width: 80%">
                    </div>
                  </div>
                </div>
                <div class="skill-bar">
                  <div class="skill-label">Responsive Design</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-purple-400" style="width: 88%">
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Tools & Software -->
            <div class="skill-card group">
              <div
                class="skill-icon bg-gradient-to-r from-orange-400 to-red-500"
              >
                <svg
                  class="w-8 h-8 text-black"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                    clip-rule="evenodd"></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-orange-400 mb-3">
                Tools & Software
              </h3>
              <div class="space-y-3">
                <div class="skill-bar">
                  <div class="skill-label">Microsoft Office</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-orange-400" style="width: 95%">
                    </div>
                  </div>
                </div>
                <div class="skill-bar">
                  <div class="skill-label">Accounting Software</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-orange-400" style="width: 90%">
                    </div>
                  </div>
                </div>
                <div class="skill-bar">
                  <div class="skill-label">Project Management</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-orange-400" style="width: 85%">
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Soft Skills -->
            <div class="skill-card group">
              <div
                class="skill-icon bg-gradient-to-r from-teal-400 to-cyan-500"
              >
                <svg
                  class="w-8 h-8 text-black"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"
                  ></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-teal-400 mb-3">Soft Skills</h3>
              <div class="space-y-3">
                <div class="skill-bar">
                  <div class="skill-label">Leadership</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-teal-400" style="width: 88%">
                    </div>
                  </div>
                </div>
                <div class="skill-bar">
                  <div class="skill-label">Communication</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-teal-400" style="width: 92%">
                    </div>
                  </div>
                </div>
                <div class="skill-bar">
                  <div class="skill-label">Problem Solving</div>
                  <div class="skill-progress">
                    <div class="skill-fill bg-teal-400" style="width: 90%">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Projects Section -->
      <section id="projects" class="py-20 relative">
        <div class="container mx-auto px-6">
          <div class="text-center mb-16">
            <h2
              class="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 mb-4"
            >
              &lt;PROJECTS_SHOWCASE/&gt;
            </h2>
            <p class="text-gray-400 font-mono">
              Featured work and achievements
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Project 1 -->
            <div class="project-card group">
              <div class="project-image">
                <div
                  class="w-full h-48 bg-gradient-to-br from-cyan-500/20 to-blue-600/20 flex items-center justify-center"
                >
                  <div class="text-center">
                    <svg
                      class="w-16 h-16 text-cyan-400 mx-auto mb-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                      ></path>
                    </svg>
                    <div class="text-cyan-400 font-mono text-sm">DASHBOARD</div>
                  </div>
                </div>
                <div class="project-overlay">
                  <div class="project-links">
                    <a href="#" class="project-link">
                      <svg
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"
                        ></path>
                        <path
                          d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"
                        ></path>
                      </svg>
                    </a>
                    <a href="#" class="project-link">
                      <svg
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                          clip-rule="evenodd"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
              <div class="project-content">
                <h3 class="text-xl font-bold text-cyan-400 mb-2">
                  Financial Dashboard
                </h3>
                <p class="text-gray-400 text-sm mb-4">
                  Interactive dashboard for financial analysis with real-time
                  data visualization and reporting capabilities.
                </p>
                <div class="project-tags">
                  <span class="project-tag">Excel</span>
                  <span class="project-tag">Power BI</span>
                  <span class="project-tag">Analytics</span>
                </div>
              </div>
            </div>

            <!-- Project 2 -->
            <div class="project-card group">
              <div class="project-image">
                <div
                  class="w-full h-48 bg-gradient-to-br from-pink-500/20 to-purple-600/20 flex items-center justify-center"
                >
                  <div class="text-center">
                    <svg
                      class="w-16 h-16 text-pink-400 mx-auto mb-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                        clip-rule="evenodd"></path>
                    </svg>
                    <div class="text-pink-400 font-mono text-sm">DESIGN</div>
                  </div>
                </div>
                <div class="project-overlay">
                  <div class="project-links">
                    <a href="#" class="project-link">
                      <svg
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"
                        ></path>
                        <path
                          d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"
                        ></path>
                      </svg>
                    </a>
                    <a href="#" class="project-link">
                      <svg
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                          clip-rule="evenodd"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
              <div class="project-content">
                <h3 class="text-xl font-bold text-pink-400 mb-2">
                  Brand Identity Design
                </h3>
                <p class="text-gray-400 text-sm mb-4">
                  Complete brand identity package including logo design, color
                  palette, and marketing materials.
                </p>
                <div class="project-tags">
                  <span class="project-tag">Adobe CC</span>
                  <span class="project-tag">Branding</span>
                  <span class="project-tag">Design</span>
                </div>
              </div>
            </div>

            <!-- Project 3 -->
            <div class="project-card group">
              <div class="project-image">
                <div
                  class="w-full h-48 bg-gradient-to-br from-green-500/20 to-emerald-600/20 flex items-center justify-center"
                >
                  <div class="text-center">
                    <svg
                      class="w-16 h-16 text-green-400 mx-auto mb-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"
                      ></path>
                    </svg>
                    <div class="text-green-400 font-mono text-sm">ANALYSIS</div>
                  </div>
                </div>
                <div class="project-overlay">
                  <div class="project-links">
                    <a href="#" class="project-link">
                      <svg
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"
                        ></path>
                        <path
                          d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"
                        ></path>
                      </svg>
                    </a>
                    <a href="#" class="project-link">
                      <svg
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                          clip-rule="evenodd"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
              <div class="project-content">
                <h3 class="text-xl font-bold text-green-400 mb-2">
                  Business Analysis Report
                </h3>
                <p class="text-gray-400 text-sm mb-4">
                  Comprehensive business analysis with data-driven insights and
                  strategic recommendations.
                </p>
                <div class="project-tags">
                  <span class="project-tag">Analysis</span>
                  <span class="project-tag">Strategy</span>
                  <span class="project-tag">Reporting</span>
                </div>
              </div>
            </div>

            <!-- Project 4 -->
            <div class="project-card group">
              <div class="project-image">
                <div
                  class="w-full h-48 bg-gradient-to-br from-purple-500/20 to-indigo-600/20 flex items-center justify-center"
                >
                  <div class="text-center">
                    <svg
                      class="w-16 h-16 text-purple-400 mx-auto mb-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                        clip-rule="evenodd"></path>
                    </svg>
                    <div class="text-purple-400 font-mono text-sm">WEB DEV</div>
                  </div>
                </div>
                <div class="project-overlay">
                  <div class="project-links">
                    <a href="#" class="project-link">
                      <svg
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"
                        ></path>
                        <path
                          d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"
                        ></path>
                      </svg>
                    </a>
                    <a href="#" class="project-link">
                      <svg
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                          clip-rule="evenodd"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
              <div class="project-content">
                <h3 class="text-xl font-bold text-purple-400 mb-2">
                  Cyberpunk Portfolio
                </h3>
                <p class="text-gray-400 text-sm mb-4">
                  Modern portfolio website with cyberpunk theme, 3D effects, and
                  interactive animations.
                </p>
                <div class="project-tags">
                  <span class="project-tag">Astro</span>
                  <span class="project-tag">Three.js</span>
                  <span class="project-tag">GSAP</span>
                </div>
              </div>
            </div>

            <!-- Project 5 -->
            <div class="project-card group">
              <div class="project-image">
                <div
                  class="w-full h-48 bg-gradient-to-br from-orange-500/20 to-red-600/20 flex items-center justify-center"
                >
                  <div class="text-center">
                    <svg
                      class="w-16 h-16 text-orange-400 mx-auto mb-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
                      ></path>
                    </svg>
                    <div class="text-orange-400 font-mono text-sm">
                      ACCOUNTING
                    </div>
                  </div>
                </div>
                <div class="project-overlay">
                  <div class="project-links">
                    <a href="#" class="project-link">
                      <svg
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"
                        ></path>
                        <path
                          d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"
                        ></path>
                      </svg>
                    </a>
                    <a href="#" class="project-link">
                      <svg
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                          clip-rule="evenodd"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
              <div class="project-content">
                <h3 class="text-xl font-bold text-orange-400 mb-2">
                  Sharia Accounting System
                </h3>
                <p class="text-gray-400 text-sm mb-4">
                  Digital accounting system designed specifically for
                  Sharia-compliant financial management.
                </p>
                <div class="project-tags">
                  <span class="project-tag">Accounting</span>
                  <span class="project-tag">Sharia</span>
                  <span class="project-tag">System</span>
                </div>
              </div>
            </div>

            <!-- Project 6 -->
            <div class="project-card group">
              <div class="project-image">
                <div
                  class="w-full h-48 bg-gradient-to-br from-teal-500/20 to-cyan-600/20 flex items-center justify-center"
                >
                  <div class="text-center">
                    <svg
                      class="w-16 h-16 text-teal-400 mx-auto mb-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                      ></path>
                    </svg>
                    <div class="text-teal-400 font-mono text-sm">DATA VIZ</div>
                  </div>
                </div>
                <div class="project-overlay">
                  <div class="project-links">
                    <a href="#" class="project-link">
                      <svg
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"
                        ></path>
                        <path
                          d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"
                        ></path>
                      </svg>
                    </a>
                    <a href="#" class="project-link">
                      <svg
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                          clip-rule="evenodd"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
              <div class="project-content">
                <h3 class="text-xl font-bold text-teal-400 mb-2">
                  Data Visualization Suite
                </h3>
                <p class="text-gray-400 text-sm mb-4">
                  Interactive data visualization tools for business intelligence
                  and performance monitoring.
                </p>
                <div class="project-tags">
                  <span class="project-tag">Visualization</span>
                  <span class="project-tag">BI</span>
                  <span class="project-tag">Analytics</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section
        id="contact"
        class="py-20 relative bg-gradient-to-t from-gray-900/50 to-transparent"
      >
        <div class="container mx-auto px-6">
          <div class="text-center mb-16">
            <h2
              class="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-green-400 mb-4"
            >
              &lt;CONTACT_INTERFACE/&gt;
            </h2>
            <p class="text-gray-400 font-mono">
              Let's connect and create something amazing
            </p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Info -->
            <div class="space-y-8">
              <div class="cyber-card">
                <h3 class="text-2xl font-bold text-cyan-400 mb-6 neon-glow">
                  Get In Touch
                </h3>
                <div class="space-y-6">
                  <div class="flex items-center space-x-4">
                    <div
                      class="w-12 h-12 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-lg flex items-center justify-center"
                    >
                      <svg
                        class="w-6 h-6 text-black"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"
                        ></path>
                        <path
                          d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"
                        ></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="text-lg font-bold text-white">Email</h4>
                      <p class="text-cyan-400"><EMAIL></p>
                    </div>
                  </div>

                  <div class="flex items-center space-x-4">
                    <div
                      class="w-12 h-12 bg-gradient-to-r from-pink-400 to-purple-500 rounded-lg flex items-center justify-center"
                    >
                      <svg
                        class="w-6 h-6 text-black"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z"
                          clip-rule="evenodd"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="text-lg font-bold text-white">LinkedIn</h4>
                      <p class="text-pink-400">muhammad-trinanda</p>
                    </div>
                  </div>

                  <div class="flex items-center space-x-4">
                    <div
                      class="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-lg flex items-center justify-center"
                    >
                      <svg
                        class="w-6 h-6 text-black"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
                          clip-rule="evenodd"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="text-lg font-bold text-white">Instagram</h4>
                      <p class="text-green-400">@trinanda321</p>
                    </div>
                  </div>

                  <div class="flex items-center space-x-4">
                    <div
                      class="w-12 h-12 bg-gradient-to-r from-purple-400 to-indigo-500 rounded-lg flex items-center justify-center"
                    >
                      <svg
                        class="w-6 h-6 text-black"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                          clip-rule="evenodd"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="text-lg font-bold text-white">Location</h4>
                      <p class="text-purple-400">Medan, Indonesia</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="cyber-card">
                <h3 class="text-xl font-bold text-pink-400 mb-4 neon-glow">
                  Available For
                </h3>
                <div class="grid grid-cols-2 gap-4">
                  <div
                    class="text-center p-4 bg-gray-800/50 rounded-lg border border-cyan-500/30"
                  >
                    <div class="text-cyan-400 font-bold">Freelance</div>
                    <div class="text-sm text-gray-400">Projects</div>
                  </div>
                  <div
                    class="text-center p-4 bg-gray-800/50 rounded-lg border border-pink-500/30"
                  >
                    <div class="text-pink-400 font-bold">Full-time</div>
                    <div class="text-sm text-gray-400">Opportunities</div>
                  </div>
                  <div
                    class="text-center p-4 bg-gray-800/50 rounded-lg border border-green-500/30"
                  >
                    <div class="text-green-400 font-bold">Consulting</div>
                    <div class="text-sm text-gray-400">Services</div>
                  </div>
                  <div
                    class="text-center p-4 bg-gray-800/50 rounded-lg border border-purple-500/30"
                  >
                    <div class="text-purple-400 font-bold">Collaboration</div>
                    <div class="text-sm text-gray-400">Projects</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Contact Form -->
            <div class="cyber-card">
              <h3 class="text-2xl font-bold text-green-400 mb-6 neon-glow">
                Send Message
              </h3>
              <ContactForm />
            </div>
          </div>
        </div>
      </section>
    </LazySection>
  </main>

  <style>
    .cyber-button {
      @apply px-8 py-4 font-mono font-bold uppercase tracking-wider transition-all duration-300 relative overflow-hidden;
      background: linear-gradient(
        45deg,
        transparent,
        rgba(0, 255, 255, 0.1),
        transparent
      );
      border: 2px solid;
      clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 100%, 20px 100%);
    }

    .cyber-button.primary {
      @apply border-cyan-400 text-cyan-400;
    }

    .cyber-button.secondary {
      @apply border-pink-400 text-pink-400;
    }

    .cyber-button:hover {
      @apply transform scale-105;
      box-shadow: 0 0 30px currentColor;
      text-shadow: 0 0 10px currentColor;
    }

    .cyber-button::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s;
    }

    .cyber-button:hover::before {
      left: 100%;
    }

    .typing-animation {
      border-right: 2px solid var(--neon-cyan);
      animation:
        typing 3s steps(40, end),
        blink-caret 0.75s step-end infinite;
    }

    .cursor {
      animation: blink 1s infinite;
    }

    @keyframes typing {
      from {
        width: 0;
      }
      to {
        width: 100%;
      }
    }

    @keyframes blink {
      0%,
      50% {
        opacity: 1;
      }
      51%,
      100% {
        opacity: 0;
      }
    }

    @keyframes blink-caret {
      from,
      to {
        border-color: transparent;
      }
      50% {
        border-color: var(--neon-cyan);
      }
    }

    /* Card Styles */
    .cyber-card {
      @apply bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-lg p-6 transition-all duration-300;
      background: linear-gradient(
        135deg,
        rgba(0, 255, 255, 0.05),
        rgba(255, 0, 255, 0.05)
      );
    }

    .cyber-card:hover {
      @apply border-cyan-500/50 transform scale-105;
      box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
    }

    .cyber-tag {
      @apply px-3 py-1 text-xs font-mono bg-gray-800/50 border border-cyan-500/30 rounded text-cyan-400 transition-all duration-300;
    }

    .cyber-tag:hover {
      @apply bg-cyan-500/20 border-cyan-400 text-cyan-300;
      box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
    }

    /* Skills Section */
    .skill-card {
      @apply bg-gray-900/70 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 transition-all duration-500;
      background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.8),
        rgba(20, 20, 20, 0.9)
      );
    }

    .skill-card:hover {
      @apply transform -translate-y-1;
      border-color: rgba(0, 255, 255, 0.5);
      box-shadow: 0 20px 40px rgba(0, 255, 255, 0.1);
    }

    .skill-icon {
      @apply w-16 h-16 rounded-lg flex items-center justify-center mb-4 mx-auto;
      box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
    }

    .skill-bar {
      @apply space-y-2;
    }

    .skill-label {
      @apply text-sm font-mono text-gray-300;
    }

    .skill-progress {
      @apply w-full bg-gray-800 rounded-full h-2 overflow-hidden;
      background: linear-gradient(
        90deg,
        rgba(0, 0, 0, 0.5),
        rgba(20, 20, 20, 0.8)
      );
    }

    .skill-fill {
      @apply h-full rounded-full transition-all duration-1000 ease-out;
      background: linear-gradient(
        90deg,
        currentColor,
        rgba(255, 255, 255, 0.8)
      );
      box-shadow: 0 0 10px currentColor;
      animation: skillLoad 2s ease-out;
    }

    @keyframes skillLoad {
      from {
        width: 0;
      }
    }

    /* Projects Section */
    .project-card {
      @apply bg-gray-900/70 backdrop-blur-sm border border-gray-700/50 rounded-xl overflow-hidden transition-all duration-500;
      background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.8),
        rgba(20, 20, 20, 0.9)
      );
    }

    .project-card:hover {
      @apply transform -translate-y-2;
      border-color: rgba(0, 255, 255, 0.5);
      box-shadow: 0 25px 50px rgba(0, 255, 255, 0.15);
    }

    .project-image {
      @apply relative overflow-hidden;
    }

    .project-image img {
      @apply transition-transform duration-500;
    }

    .project-card:hover .project-image img {
      @apply transform scale-110;
    }

    .project-overlay {
      @apply absolute inset-0 bg-black/70 flex items-center justify-center opacity-0 transition-opacity duration-300;
    }

    .project-card:hover .project-overlay {
      @apply opacity-100;
    }

    .project-links {
      @apply flex space-x-4;
    }

    .project-link {
      @apply w-12 h-12 bg-cyan-400/20 border-2 border-cyan-400 rounded-full flex items-center justify-center text-cyan-400 transition-all duration-300;
    }

    .project-link:hover {
      @apply bg-cyan-400 text-black transform scale-110;
      box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
    }

    .project-content {
      @apply p-6;
    }

    .project-tags {
      @apply flex flex-wrap gap-2;
    }

    .project-tag {
      @apply px-2 py-1 text-xs font-mono bg-gray-800/50 border border-gray-600/50 rounded text-gray-400 transition-all duration-300;
    }

    .project-tag:hover {
      @apply bg-cyan-500/20 border-cyan-400/50 text-cyan-400;
    }

    /* Contact Form */
    .cyber-input {
      @apply w-full px-4 py-3 bg-gray-900/50 border border-gray-700/50 rounded-lg text-white placeholder-gray-500 font-mono transition-all duration-300;
      background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.8),
        rgba(20, 20, 20, 0.9)
      );
    }

    .cyber-input:focus {
      @apply outline-none border-cyan-500/50;
      box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
    }

    /* Remove bottom space completely */
    main {
      margin-bottom: 0 !important;
      padding-bottom: 0 !important;
    }

    section:last-child {
      margin-bottom: 0 !important;
      padding-bottom: 0 !important;
    }

    #playground {
      margin-bottom: 0 !important;
      padding-bottom: 0 !important;
    }

    /* Specific fixes for CodePlayground space */
    #code-playground {
      margin-bottom: 0 !important;
      padding-bottom: 0 !important;
    }

    #playground .container {
      margin-bottom: 0 !important;
      padding-bottom: 0 !important;
    }

    #playground > div {
      margin-bottom: 0 !important;
      padding-bottom: 0 !important;
    }

    /* Force remove space after playground */
    #playground::after {
      display: none !important;
    }

    /* Ensure no space between playground and footer */
    #playground + * {
      margin-top: 0 !important;
    }

    /* NUCLEAR OPTION - Force remove ALL space */
    body {
      height: auto !important;
      min-height: 100vh !important;
      max-height: none !important;
    }

    html {
      height: auto !important;
      min-height: 100vh !important;
      max-height: none !important;
    }

    /* Remove space from wrapper */
    .relative.z-10 {
      margin-bottom: 0 !important;
      padding-bottom: 0 !important;
      height: auto !important;
    }

    /* Force footer to stick */
    footer {
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      padding-bottom: 0 !important;
    }

    /* Responsive Improvements */
    @media (max-width: 768px) {
      .cyber-button {
        @apply px-6 py-3 text-sm;
      }

      .skill-card {
        @apply p-4;
      }

      .project-card {
        @apply mx-2;
      }
    }
  </style>

  <script>
    // Typing animation
    const texts = [
      "Sharia Accounting Student",
      "Graphic Designer",
      "Data Analyst",
      "Web Developer",
      "Financial Expert",
    ];

    let textIndex = 0;
    let charIndex = 0;
    let isDeleting = false;
    const typedElement = document.getElementById("typed-text");

    function typeWriter() {
      if (!typedElement) return;

      const currentText = texts[textIndex];

      if (isDeleting) {
        typedElement.textContent = currentText.substring(0, charIndex - 1);
        charIndex--;
      } else {
        typedElement.textContent = currentText.substring(0, charIndex + 1);
        charIndex++;
      }

      let typeSpeed = isDeleting ? 50 : 100;

      if (!isDeleting && charIndex === currentText.length) {
        typeSpeed = 2000;
        isDeleting = true;
      } else if (isDeleting && charIndex === 0) {
        isDeleting = false;
        textIndex = (textIndex + 1) % texts.length;
        typeSpeed = 500;
      }

      setTimeout(typeWriter, typeSpeed);
    }

    // Start typing animation when element is available
    if (typedElement) {
      typeWriter();
    }

    // Skill bars animation on scroll
    const observerOptions = {
      threshold: 0.5,
      rootMargin: "0px 0px -100px 0px",
    };

    const skillObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const skillFills = entry.target.querySelectorAll(".skill-fill");
          skillFills.forEach((fill) => {
            (fill as HTMLElement).style.animation =
              "skillLoad 2s ease-out forwards";
          });
        }
      });
    }, observerOptions);

    // Observe skills section
    const skillsSection = document.getElementById("skills");
    if (skillsSection) {
      skillObserver.observe(skillsSection);
    }

    // Smooth scroll for navigation links
    document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
      anchor.addEventListener("click", (e: Event) => {
        e.preventDefault();
        const target = document.querySelector(
          (anchor as HTMLAnchorElement).getAttribute("href") || ""
        );
        if (target) {
          target.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      });
    });

    // Add parallax effect to background elements
    window.addEventListener("scroll", () => {
      const scrolled = window.pageYOffset;
      const parallaxElements = document.querySelectorAll(".absolute");

      parallaxElements.forEach((element, index) => {
        const speed = 0.5 + index * 0.1;
        (element as HTMLElement).style.transform =
          `translateY(${scrolled * speed}px)`;
      });
    });

    // Scroll animations
    const observeElements = () => {
      const elements = document.querySelectorAll(".animate-on-scroll");

      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              entry.target.classList.add("animated");
            }
          });
        },
        {
          threshold: 0.1,
          rootMargin: "0px 0px -50px 0px",
        }
      );

      elements.forEach((el) => observer.observe(el));
    };

    // Initialize scroll animations
    observeElements();

    // Add scroll animation classes to specific elements only (not all sections)
    document.addEventListener("DOMContentLoaded", () => {
      // Only add animation to cards, not main sections
      const cards = document.querySelectorAll(
        ".cyber-card, .skill-card, .project-card"
      );
      cards.forEach((card, index) => {
        (card as HTMLElement).style.animationDelay = `${index * 0.1}s`;
        card.classList.add("animate-on-scroll");
      });

      // Add animation to specific elements that should animate
      const animateElements = document.querySelectorAll(".animate-element");
      animateElements.forEach((element, index) => {
        (element as HTMLElement).style.animationDelay = `${index * 0.2}s`;
        element.classList.add("animate-on-scroll");
      });
    });
  </script>

  <!-- Initialize GSAP animations -->
  <script>
    import("../scripts/gsap-animations.js").then(
      ({ default: AnimationController }) => {
        new AnimationController();
      }
    );
  </script>

  <!-- Advanced Components -->
  <AnalyticsDashboard />

  <!-- Code Playground Section -->
  <section
    id="playground"
    class="py-6 bg-gray-900/50"
    style="margin-bottom: 0 !important; padding-bottom: 0 !important; border-bottom: none !important;"
  >
    <div
      class="container mx-auto px-6"
      style="margin-bottom: 0 !important; padding-bottom: 0 !important;"
    >
      <div class="text-center mb-4">
        <h2
          class="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent"
        >
          Interactive Code Playground
        </h2>
        <p class="text-gray-300 max-w-2xl mx-auto">
          Experiment with code directly in your browser. Try out JavaScript,
          HTML, CSS, and TypeScript examples.
        </p>
      </div>
      <div style="margin-bottom: 0 !important; padding-bottom: 0 !important;">
        <CodePlayground />
      </div>
    </div>
  </section>
</Layout>

<style>
  /* FORCE REMOVE SPACE AFTER PLAYGROUND SECTION */
  #playground {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
    border-bottom: none !important;
    overflow: hidden !important;
  }

  #playground::after,
  #playground::before {
    display: none !important;
    content: none !important;
    height: 0 !important;
  }

  /* Ensure no space between playground and footer */
  #playground + footer,
  #playground ~ footer {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }

  /* Force body to end immediately after content */
  body {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  /* Aggressive fix for main container */
  main {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  main > *:last-child {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  /* Ensure Three.js background doesn't create layout issues */
  #three-background {
    position: fixed !important;
    z-index: -1 !important;
    pointer-events: none !important;
  }
</style>
