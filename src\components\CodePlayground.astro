---
// Interactive Code Playground Component
---

<div
  id="code-playground"
  class="w-full max-w-4xl mx-auto bg-gray-900/90 backdrop-blur-sm border border-cyan-500/30 rounded-lg overflow-hidden"
>
  <!-- Playground Header -->
  <div class="bg-gray-800/80 border-b border-gray-700 p-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <h3 class="text-cyan-400 font-mono text-lg font-semibold">
          Code Playground
        </h3>
        <div class="flex space-x-2">
          <button class="lang-btn active" data-lang="javascript"
            >JavaScript</button
          >
          <button class="lang-btn" data-lang="html">HTML</button>
          <button class="lang-btn" data-lang="css">CSS</button>
          <button class="lang-btn" data-lang="typescript">TypeScript</button>
        </div>
      </div>
      <div class="flex items-center space-x-2">
        <button
          id="run-code"
          class="bg-green-500/20 hover:bg-green-500/30 text-green-400 px-4 py-2 rounded font-mono text-sm transition-colors"
        >
          ▶ Run
        </button>
        <button
          id="clear-code"
          class="bg-red-500/20 hover:bg-red-500/30 text-red-400 px-4 py-2 rounded font-mono text-sm transition-colors"
        >
          Clear
        </button>
        <button
          id="share-code"
          class="bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 px-4 py-2 rounded font-mono text-sm transition-colors"
        >
          Share
        </button>
      </div>
    </div>
  </div>

  <!-- Code Editor and Output -->
  <div class="grid grid-cols-1 lg:grid-cols-2 h-64">
    <!-- Code Editor -->
    <div class="border-r border-gray-700">
      <div class="bg-gray-800/50 px-4 py-2 border-b border-gray-700">
        <span class="text-gray-400 font-mono text-sm">Editor</span>
      </div>
      <div class="relative h-full">
        <textarea
          id="code-editor"
          class="w-full h-full bg-gray-900 text-white font-mono text-sm p-4 resize-none focus:outline-none"
          placeholder="// Write your code here..."
          spellcheck="false"></textarea>
        <div
          id="line-numbers"
          class="absolute left-0 top-0 bg-gray-800/50 text-gray-500 font-mono text-sm p-4 pointer-events-none select-none"
        >
          1
        </div>
      </div>
    </div>

    <!-- Output Panel -->
    <div>
      <div
        class="bg-gray-800/50 px-4 py-2 border-b border-gray-700 flex items-center justify-between"
      >
        <span class="text-gray-400 font-mono text-sm">Output</span>
        <div class="flex space-x-2">
          <button class="output-tab active" data-tab="console">Console</button>
          <button class="output-tab" data-tab="preview">Preview</button>
        </div>
      </div>

      <!-- Console Output -->
      <div
        id="console-output"
        class="h-full bg-gray-900 p-4 font-mono text-sm overflow-auto"
      >
        <div class="text-gray-500">// Console output will appear here</div>
      </div>

      <!-- Preview Output -->
      <div id="preview-output" class="h-full bg-white hidden">
        <iframe id="preview-frame" class="w-full h-full border-none"></iframe>
      </div>
    </div>
  </div>

  <!-- Code Examples -->
  <div class="bg-gray-800/50 border-t border-gray-700 p-4">
    <div class="flex items-center justify-between mb-3">
      <span class="text-gray-400 font-mono text-sm">Examples</span>
      <button
        id="toggle-examples"
        class="text-cyan-400 hover:text-cyan-300 text-sm"
      >
        Show Examples
      </button>
    </div>
    <div id="examples-container" class="hidden space-y-2">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
        <button class="example-btn" data-example="hello-world"
          >Hello World</button
        >
        <button class="example-btn" data-example="dom-manipulation"
          >DOM Manipulation</button
        >
        <button class="example-btn" data-example="async-fetch"
          >Async/Await</button
        >
        <button class="example-btn" data-example="array-methods"
          >Array Methods</button
        >
        <button class="example-btn" data-example="css-animation"
          >CSS Animation</button
        >
        <button class="example-btn" data-example="three-js-basic"
          >Three.js Basic</button
        >
      </div>
    </div>
  </div>
</div>

<script>
  class CodePlayground {
    constructor() {
      this.currentLanguage = "javascript";
      this.currentTab = "console";
      this.code = "";
      this.examples = this.getExamples();

      this.init();
    }

    init() {
      this.setupEventListeners();
      this.setupLineNumbers();
      this.loadExample("hello-world");
    }

    setupEventListeners() {
      const editor = document.getElementById("code-editor");
      const runBtn = document.getElementById("run-code");
      const clearBtn = document.getElementById("clear-code");
      const shareBtn = document.getElementById("share-code");
      const toggleExamples = document.getElementById("toggle-examples");

      // Language buttons
      document.querySelectorAll(".lang-btn").forEach((btn) => {
        btn.addEventListener("click", (e) =>
          this.switchLanguage(e.target.dataset.lang)
        );
      });

      // Output tabs
      document.querySelectorAll(".output-tab").forEach((btn) => {
        btn.addEventListener("click", (e) =>
          this.switchOutputTab(e.target.dataset.tab)
        );
      });

      // Example buttons
      document.querySelectorAll(".example-btn").forEach((btn) => {
        btn.addEventListener("click", (e) =>
          this.loadExample(e.target.dataset.example)
        );
      });

      // Main controls
      editor?.addEventListener("input", (e) => this.updateCode(e.target.value));
      editor?.addEventListener("scroll", () => this.syncLineNumbers());
      runBtn?.addEventListener("click", () => this.runCode());
      clearBtn?.addEventListener("click", () => this.clearCode());
      shareBtn?.addEventListener("click", () => this.shareCode());
      toggleExamples?.addEventListener("click", () => this.toggleExamples());

      // Keyboard shortcuts
      editor?.addEventListener("keydown", (e) => this.handleKeyboard(e));
    }

    setupLineNumbers() {
      const editor = document.getElementById("code-editor");
      const lineNumbers = document.getElementById("line-numbers");

      const updateLineNumbers = () => {
        const lines = editor.value.split("\n").length;
        const numbers = Array.from({ length: lines }, (_, i) => i + 1).join(
          "\n"
        );
        lineNumbers.textContent = numbers;
      };

      editor?.addEventListener("input", updateLineNumbers);
      updateLineNumbers();
    }

    syncLineNumbers() {
      const editor = document.getElementById("code-editor");
      const lineNumbers = document.getElementById("line-numbers");
      lineNumbers.scrollTop = editor.scrollTop;
    }

    switchLanguage(language) {
      this.currentLanguage = language;

      // Update active button
      document.querySelectorAll(".lang-btn").forEach((btn) => {
        btn.classList.remove("active");
      });
      document
        .querySelector(`[data-lang="${language}"]`)
        ?.classList.add("active");

      // Update editor placeholder
      const editor = document.getElementById("code-editor");
      const placeholders = {
        javascript: "// Write your JavaScript code here...",
        html: "<!-- Write your HTML code here -->",
        css: "/* Write your CSS code here */",
        typescript: "// Write your TypeScript code here...",
      };

      editor.placeholder =
        placeholders[language] || "// Write your code here...";
    }

    switchOutputTab(tab) {
      this.currentTab = tab;

      // Update active tab
      document.querySelectorAll(".output-tab").forEach((btn) => {
        btn.classList.remove("active");
      });
      document.querySelector(`[data-tab="${tab}"]`)?.classList.add("active");

      // Show/hide output panels
      const consoleOutput = document.getElementById("console-output");
      const previewOutput = document.getElementById("preview-output");

      if (tab === "console") {
        consoleOutput.classList.remove("hidden");
        previewOutput.classList.add("hidden");
      } else {
        consoleOutput.classList.add("hidden");
        previewOutput.classList.remove("hidden");
      }
    }

    updateCode(code) {
      this.code = code;
    }

    runCode() {
      const consoleOutput = document.getElementById("console-output");
      const previewFrame = document.getElementById("preview-frame");

      // Clear previous output
      consoleOutput.innerHTML = "";

      try {
        if (this.currentLanguage === "javascript") {
          this.runJavaScript();
        } else if (this.currentLanguage === "html") {
          this.runHTML();
        } else if (this.currentLanguage === "css") {
          this.runCSS();
        } else if (this.currentLanguage === "typescript") {
          this.runTypeScript();
        }
      } catch (error) {
        this.logToConsole(`Error: ${error.message}`, "error");
      }
    }

    runJavaScript() {
      const originalConsole = window.console;
      const logs = [];

      // Override console methods
      window.console = {
        log: (...args) => {
          logs.push({ type: "log", args });
          originalConsole.log(...args);
        },
        error: (...args) => {
          logs.push({ type: "error", args });
          originalConsole.error(...args);
        },
        warn: (...args) => {
          logs.push({ type: "warn", args });
          originalConsole.warn(...args);
        },
      };

      try {
        // Execute code in a safe context
        const func = new Function(this.code);
        const result = func();

        if (result !== undefined) {
          logs.push({ type: "result", args: [result] });
        }
      } catch (error) {
        logs.push({ type: "error", args: [error.message] });
      } finally {
        // Restore original console
        window.console = originalConsole;
      }

      // Display logs
      logs.forEach((log) => {
        this.logToConsole(log.args.join(" "), log.type);
      });

      if (logs.length === 0) {
        this.logToConsole("Code executed successfully (no output)", "info");
      }
    }

    runHTML() {
      const previewFrame = document.getElementById("preview-frame");
      const doc =
        previewFrame.contentDocument || previewFrame.contentWindow.document;

      doc.open();
      doc.write(this.code);
      doc.close();

      this.switchOutputTab("preview");
      this.logToConsole("HTML rendered in preview", "info");
    }

    runCSS() {
      const previewFrame = document.getElementById("preview-frame");
      const doc =
        previewFrame.contentDocument || previewFrame.contentWindow.document;

      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <style>${this.code}</style>
        </head>
        <body>
          <div class="demo-content">
            <h1>CSS Demo</h1>
            <p>This is a paragraph to demonstrate your CSS.</p>
            <button>Button</button>
            <div class="box">Box</div>
          </div>
        </body>
        </html>
      `;

      doc.open();
      doc.write(html);
      doc.close();

      this.switchOutputTab("preview");
      this.logToConsole("CSS applied to demo content", "info");
    }

    runTypeScript() {
      // For simplicity, treat TypeScript as JavaScript
      this.logToConsole(
        "Note: TypeScript is being executed as JavaScript",
        "warn"
      );
      this.runJavaScript();
    }

    logToConsole(message, type = "log") {
      const consoleOutput = document.getElementById("console-output");
      const logElement = document.createElement("div");

      const colors = {
        log: "text-white",
        error: "text-red-400",
        warn: "text-yellow-400",
        info: "text-blue-400",
        result: "text-green-400",
      };

      logElement.className = `mb-1 ${colors[type] || "text-white"}`;
      logElement.textContent = `> ${message}`;

      consoleOutput.appendChild(logElement);
      consoleOutput.scrollTop = consoleOutput.scrollHeight;
    }

    clearCode() {
      const editor = document.getElementById("code-editor");
      const consoleOutput = document.getElementById("console-output");

      editor.value = "";
      this.code = "";
      consoleOutput.innerHTML =
        '<div class="text-gray-500">// Console cleared</div>';

      this.setupLineNumbers();
    }

    shareCode() {
      const shareData = {
        language: this.currentLanguage,
        code: this.code,
        timestamp: new Date().toISOString(),
      };

      const shareUrl = `data:text/plain;charset=utf-8,${encodeURIComponent(JSON.stringify(shareData, null, 2))}`;

      // Create temporary download link
      const link = document.createElement("a");
      link.href = shareUrl;
      link.download = `code-playground-${Date.now()}.json`;
      link.click();

      this.logToConsole("Code exported successfully", "info");
    }

    toggleExamples() {
      const container = document.getElementById("examples-container");
      const button = document.getElementById("toggle-examples");

      if (container.classList.contains("hidden")) {
        container.classList.remove("hidden");
        button.textContent = "Hide Examples";
      } else {
        container.classList.add("hidden");
        button.textContent = "Show Examples";
      }
    }

    loadExample(exampleKey) {
      const example = this.examples[exampleKey];
      if (example) {
        const editor = document.getElementById("code-editor");
        editor.value = example.code;
        this.code = example.code;
        this.switchLanguage(example.language);
        this.setupLineNumbers();

        this.logToConsole(`Loaded example: ${example.title}`, "info");
      }
    }

    handleKeyboard(e) {
      // Ctrl/Cmd + Enter to run code
      if ((e.ctrlKey || e.metaKey) && e.key === "Enter") {
        e.preventDefault();
        this.runCode();
      }

      // Tab for indentation
      if (e.key === "Tab") {
        e.preventDefault();
        const editor = e.target;
        const start = editor.selectionStart;
        const end = editor.selectionEnd;

        editor.value =
          editor.value.substring(0, start) + "  " + editor.value.substring(end);
        editor.selectionStart = editor.selectionEnd = start + 2;

        this.updateCode(editor.value);
        this.setupLineNumbers();
      }
    }

    getExamples() {
      return {
        "hello-world": {
          title: "Hello World",
          language: "javascript",
          code: `console.log('Hello, World!');
console.log('Welcome to the Code Playground!');

// Try some basic operations
const name = 'Developer';
const greeting = \`Hello, \${name}!\`;
console.log(greeting);`,
        },
        "dom-manipulation": {
          title: "DOM Manipulation",
          language: "javascript",
          code: `// Create and manipulate DOM elements
const div = document.createElement('div');
div.textContent = 'Dynamic Content';
div.style.color = '#00ffff';
div.style.padding = '10px';
div.style.border = '1px solid #00ffff';

console.log('Element created:', div.outerHTML);`,
        },
        "async-fetch": {
          title: "Async/Await Example",
          language: "javascript",
          code: `// Async/await example
async function fetchData() {
  try {
    console.log('Fetching data...');
    
    // Simulate API call
    const response = await new Promise(resolve => {
      setTimeout(() => {
        resolve({ data: 'Sample data', status: 200 });
      }, 1000);
    });
    
    console.log('Data received:', response);
    return response;
  } catch (error) {
    console.error('Error:', error);
  }
}

fetchData();`,
        },
        "array-methods": {
          title: "Array Methods",
          language: "javascript",
          code: `// Array methods demonstration
const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

console.log('Original array:', numbers);

const doubled = numbers.map(n => n * 2);
console.log('Doubled:', doubled);

const evens = numbers.filter(n => n % 2 === 0);
console.log('Even numbers:', evens);

const sum = numbers.reduce((acc, n) => acc + n, 0);
console.log('Sum:', sum);`,
        },
        "css-animation": {
          title: "CSS Animation",
          language: "css",
          code: `/* CSS Animation Example */
.demo-content {
  padding: 20px;
  font-family: 'Courier New', monospace;
}

.box {
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #00ffff, #ff00ff);
  margin: 20px 0;
  animation: pulse 2s infinite;
  border-radius: 10px;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

button {
  background: #00ffff;
  color: #000;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

button:hover {
  background: #ff00ff;
  transform: translateY(-2px);
}`,
        },
        "three-js-basic": {
          title: "Three.js Basic",
          language: "javascript",
          code: `// Three.js basic example (conceptual)
console.log('Three.js Basic Scene Setup');

// Scene setup
const scene = {
  background: 0x000000,
  objects: []
};

// Camera setup
const camera = {
  position: { x: 0, y: 0, z: 5 },
  fov: 75,
  aspect: window.innerWidth / window.innerHeight
};

// Geometry and material
const geometry = {
  type: 'BoxGeometry',
  width: 1, height: 1, depth: 1
};

const material = {
  color: 0x00ffff,
  wireframe: true
};

console.log('Scene:', scene);
console.log('Camera:', camera);
console.log('Geometry:', geometry);
console.log('Material:', material);

console.log('Three.js scene would be rendered here!');`,
        },
      };
    }
  }

  // Initialize when DOM is ready
  document.addEventListener("DOMContentLoaded", () => {
    window.codePlayground = new CodePlayground();
  });
</script>

<style>
  /* AGGRESSIVE REMOVAL OF ALL BOTTOM SPACING FROM CODEPLAYGROUND */
  #code-playground {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
    border-bottom: none !important;
    overflow: hidden !important;
  }

  /* Force remove any pseudo-elements that might create space */
  #code-playground::after,
  #code-playground::before {
    display: none !important;
    content: none !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Force all children to have no bottom spacing */
  #code-playground > * {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  #code-playground > *:last-child {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  /* Force grid container to have no bottom spacing */
  #code-playground .grid {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  /* Force all nested elements to have no bottom spacing */
  #code-playground * {
    margin-bottom: 0 !important;
  }

  #code-playground *:last-child {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  .lang-btn {
    @apply px-3 py-1 text-xs font-mono bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors;
  }

  .lang-btn.active {
    @apply bg-cyan-500/20 text-cyan-400 border border-cyan-500/30;
  }

  .output-tab {
    @apply px-3 py-1 text-xs font-mono bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors;
  }

  .output-tab.active {
    @apply bg-purple-500/20 text-purple-400 border border-purple-500/30;
  }

  .example-btn {
    @apply px-3 py-2 text-xs font-mono bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors text-left;
  }

  #code-editor {
    padding-left: 3rem;
  }

  #line-numbers {
    width: 3rem;
    text-align: right;
    padding-right: 0.5rem;
    border-right: 1px solid #374151;
  }
</style>
